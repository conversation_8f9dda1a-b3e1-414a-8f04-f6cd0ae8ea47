import React, { useState, useEffect } from 'react';
import { Upload, Camera, Eye, BarChart3, Download, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Zap, Bug, Droplets, Leaf, Target, MapPin, Layers, Activity, TrendingUp, Grid, Map, Palette } from 'lucide-react';

interface Farm {
  id: number;
  name: string;
  total_area: number;
}

interface DroneImage {
  id: number;
  farm_id: number;
  field_name: string;
  filename: string;
  original_filename: string;
  image_type: 'rgb' | 'nir' | 'multispectral' | 'thermal';
  latitude?: number;
  longitude?: number;
  altitude?: number;
  capture_date?: string;
  weather_conditions?: string;
  analysis_results?: AnalysisResult;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
}

interface PestDetection {
  type: string;
  confidence: number;
  location: { x: number; y: number; width: number; height: number };
  severity: 'low' | 'medium' | 'high';
  recommended_action: string;
}

interface DiseaseDetection {
  disease_name: string;
  confidence: number;
  affected_area_percentage: number;
  severity: 'early' | 'moderate' | 'severe';
  location_zones: Array<{ x: number; y: number; radius: number }>;
  treatment_recommendation: string;
}

interface StressAnalysis {
  water_stress: {
    level: 'none' | 'mild' | 'moderate' | 'severe';
    affected_percentage: number;
    zones: Array<{ x: number; y: number; intensity: number }>;
  };
  nutrient_deficiency: {
    type: string[];
    severity: 'mild' | 'moderate' | 'severe';
    affected_areas: Array<{ x: number; y: number; deficiency_type: string }>;
  };
}

interface SprayRecommendation {
  zones: Array<{
    id: string;
    coordinates: Array<{ lat: number; lng: number }>;
    area_hectares: number;
    problem_type: 'pest' | 'disease' | 'nutrient' | 'weed';
    severity: 'low' | 'medium' | 'high';
    product_recommendation: string;
    application_rate: string;
    priority: number;
    estimated_cost: number;
  }>;
  total_area: number;
  estimated_savings: number;
  application_sequence: string[];
}

interface AnalysisResult {
  ndvi_analysis?: {
    success: boolean;
    mean_ndvi: number;
    vegetation_percentage: number;
    healthy_vegetation_percentage: number;
    stress_areas: Array<{ x: number; y: number; ndvi_value: number }>;
    growth_stage: string;
    biomass_estimate: number;
  };
  pest_detection?: {
    success: boolean;
    total_detections: number;
    pests_found: PestDetection[];
    risk_level: 'low' | 'medium' | 'high';
  };
  disease_analysis?: {
    success: boolean;
    diseases_detected: DiseaseDetection[];
    overall_health_score: number;
    infection_risk: 'low' | 'medium' | 'high';
  };
  stress_analysis?: StressAnalysis;
  weed_detection?: {
    weed_coverage_percentage: number;
    weed_types: string[];
    density_map: Array<{ x: number; y: number; density: number }>;
  };
  spray_recommendations?: SprayRecommendation;
  comparative_analysis?: {
    previous_flight_comparison: {
      ndvi_change: number;
      health_improvement: number;
      new_issues_detected: number;
    };
  };
}

const DroneImageAnalysis: React.FC = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarm, setSelectedFarm] = useState<Farm | null>(null);
  const [images, setImages] = useState<DroneImage[]>([]);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [analysisProgress, setAnalysisProgress] = useState<{ [key: number]: string }>({});
  const [dragActive, setDragActive] = useState(false);
  const [activeTab, setActiveTab] = useState<'analysis' | 'orthomosaic' | 'indices' | 'maps'>('analysis');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'rgb' | 'nir' | 'multispectral' | 'thermal'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'processing' | 'completed' | 'failed'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [groupByField, setGroupByField] = useState(false);

  // Dados das fazendas com talhões
  useEffect(() => {
    const mockFarms: Farm[] = [
      { id: 1, name: 'Fazenda São João', total_area: 2450 },
      { id: 2, name: 'Fazenda Esperança', total_area: 1890 },
      { id: 3, name: 'Fazenda Progresso', total_area: 3200 }
    ];
    setFarms(mockFarms);
  }, []);

  // Funções de Upload e Drag & Drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files: FileList) => {
    if (!selectedFarm) {
      alert('Selecione uma fazenda primeiro');
      return;
    }

    Array.from(files).forEach((file, index) => {
      // Validar tipo de arquivo
      const validTypes = ['image/jpeg', 'image/png', 'image/tiff', 'image/tif'];
      if (!validTypes.includes(file.type) && !file.name.toLowerCase().endsWith('.tif')) {
        alert(`Arquivo ${file.name} não é um tipo de imagem válido`);
        return;
      }

      // Validar tamanho (50MB)
      if (file.size > 50 * 1024 * 1024) {
        alert(`Arquivo ${file.name} é muito grande. Máximo 50MB`);
        return;
      }

      // Simular upload
      const uploadId = `upload_${Date.now()}_${index}`;
      setUploadProgress(prev => ({ ...prev, [uploadId]: 0 }));

      // Simular progresso de upload
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          const currentProgress = prev[uploadId] || 0;
          const newProgress = Math.min(currentProgress + Math.random() * 20, 100);

          if (newProgress >= 100) {
            clearInterval(interval);

            // Criar nova imagem após upload completo
            const newImage: DroneImage = {
              id: Date.now() + index,
              farm_id: selectedFarm.id,
              field_name: `Talhão ${String.fromCharCode(65 + Math.floor(Math.random() * 3))}${Math.floor(Math.random() * 5) + 1}`,
              filename: file.name,
              original_filename: file.name,
              image_type: detectImageType(file.name),
              latitude: -12.5489 + (Math.random() - 0.5) * 0.01,
              longitude: -55.7183 + (Math.random() - 0.5) * 0.01,
              altitude: Math.floor(Math.random() * 50) + 100,
              capture_date: new Date().toISOString(),
              weather_conditions: getRandomWeather(),
              processing_status: 'completed',
              created_at: new Date().toISOString()
            };

            setImages(prev => [newImage, ...prev]);

            // Remover progresso após delay
            setTimeout(() => {
              setUploadProgress(prev => {
                const newProgress = { ...prev };
                delete newProgress[uploadId];
                return newProgress;
              });
            }, 1000);
          }

          return { ...prev, [uploadId]: newProgress };
        });
      }, 500);
    });
  };

  const detectImageType = (filename: string): 'rgb' | 'nir' | 'multispectral' | 'thermal' => {
    const name = filename.toLowerCase();
    if (name.includes('nir') || name.includes('infrared')) return 'nir';
    if (name.includes('multi') || name.includes('spectral')) return 'multispectral';
    if (name.includes('thermal') || name.includes('temp')) return 'thermal';
    return 'rgb';
  };

  const getRandomWeather = (): string => {
    const conditions = [
      'Ensolarado, 28°C, vento 5km/h',
      'Parcialmente nublado, 26°C, vento 8km/h',
      'Céu limpo, 30°C, vento 3km/h',
      'Nublado, 24°C, vento 12km/h'
    ];
    return conditions[Math.floor(Math.random() * conditions.length)];
  };

  // Carregar imagens quando uma fazenda é selecionada
  useEffect(() => {
    if (selectedFarm) {
      const mockImages: DroneImage[] = [
        {
          id: 1,
          farm_id: selectedFarm.id,
          field_name: 'Talhão A1 - Soja',
          filename: 'drone_001_rgb.jpg',
          original_filename: 'TALHAO_A1_RGB_20240620.jpg',
          image_type: 'rgb',
          latitude: -12.5489,
          longitude: -55.7183,
          altitude: 120,
          capture_date: '2024-06-20T10:30:00',
          weather_conditions: 'Ensolarado, 28°C, vento 5km/h',
          processing_status: 'completed',
          created_at: '2024-06-20T10:35:00'
        },
        {
          id: 2,
          farm_id: selectedFarm.id,
          field_name: 'Talhão A1 - Soja',
          filename: 'drone_002_nir.jpg',
          original_filename: 'TALHAO_A1_NIR_20240620.jpg',
          image_type: 'nir',
          latitude: -12.5489,
          longitude: -55.7183,
          altitude: 120,
          capture_date: '2024-06-20T10:32:00',
          weather_conditions: 'Ensolarado, 28°C, vento 5km/h',
          processing_status: 'completed',
          created_at: '2024-06-20T10:37:00'
        },
        {
          id: 3,
          farm_id: selectedFarm.id,
          field_name: 'Talhão B2 - Milho',
          filename: 'drone_003_multi.jpg',
          original_filename: 'TALHAO_B2_MULTI_20240621.jpg',
          image_type: 'multispectral',
          latitude: -12.5495,
          longitude: -55.7190,
          altitude: 100,
          capture_date: '2024-06-21T09:15:00',
          weather_conditions: 'Parcialmente nublado, 26°C',
          processing_status: 'processing',
          created_at: '2024-06-21T09:20:00'
        }
      ];
      setImages(mockImages);
    }
  }, [selectedFarm]);

  const handleAnalyzeImage = (imageId: number) => {
    setAnalysisProgress(prev => ({ ...prev, [imageId]: 'processing' }));

    // Simular análise com IA
    setTimeout(() => {
      setImages(prev => prev.map(img =>
        img.id === imageId
          ? { ...img, processing_status: 'completed' as const }
          : img
      ));
      setAnalysisProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[imageId];
        return newProgress;
      });
    }, 3000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'processing': return <Clock className="w-5 h-5 text-yellow-600 animate-spin" />;
      case 'failed': return <AlertTriangle className="w-5 h-5 text-red-600" />;
      default: return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'rgb': return <Camera className="w-4 h-4 text-blue-600" />;
      case 'nir': return <Zap className="w-4 h-4 text-purple-600" />;
      case 'multispectral': return <Layers className="w-4 h-4 text-green-600" />;
      case 'thermal': return <Activity className="w-4 h-4 text-red-600" />;
      default: return <Camera className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rgb': return 'bg-blue-100 text-blue-700';
      case 'nir': return 'bg-purple-100 text-purple-700';
      case 'multispectral': return 'bg-green-100 text-green-700';
      case 'thermal': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const filteredImages = images.filter(image => {
    const matchesSearch = image.field_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         image.original_filename.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || image.image_type === filterType;
    const matchesStatus = filterStatus === 'all' || image.processing_status === filterStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Análise de Imagens de Drone</h1>
          <p className="text-gray-600">Upload e análise inteligente de imagens capturadas por drones</p>
        </div>

        {/* Farm Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Selecionar Fazenda</label>
          <select
            value={selectedFarm?.id || ''}
            onChange={(e) => {
              const farm = farms.find(f => f.id === parseInt(e.target.value));
              setSelectedFarm(farm || null);
            }}
            className="w-full md:w-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
          >
            <option value="">Selecione uma fazenda</option>
            {farms.map(farm => (
              <option key={farm.id} value={farm.id}>{farm.name}</option>
            ))}
          </select>
        </div>

        {selectedFarm && (
          <>
            {/* Upload Area */}
            <div className="mb-8">
              <div
                className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                  dragActive
                    ? 'border-emerald-500 bg-emerald-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Faça upload das imagens de drone
                </h3>
                <p className="text-gray-600 mb-4">
                  Arraste e solte os arquivos aqui ou clique para selecionar
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Suporta: JPEG, PNG, TIFF • Máximo: 50MB por arquivo
                </p>
                <input
                  type="file"
                  multiple
                  accept="image/*,.tif,.tiff"
                  onChange={handleFileInput}
                  className="hidden"
                  id="file-upload"
                />
                <label
                  htmlFor="file-upload"
                  className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors cursor-pointer inline-flex items-center space-x-2"
                >
                  <Upload className="w-4 h-4" />
                  <span>Selecionar Arquivos</span>
                </label>
              </div>

              {/* Upload Progress */}
              {Object.keys(uploadProgress).length > 0 && (
                <div className="mt-4 space-y-2">
                  {Object.entries(uploadProgress).map(([uploadId, progress]) => (
                    <div key={uploadId} className="bg-white rounded-lg p-3 border border-gray-200">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-900">Fazendo upload...</span>
                        <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-emerald-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Filters and Search */}
            <div className="mb-6 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Buscar</label>
                  <input
                    type="text"
                    placeholder="Nome do talhão ou arquivo..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tipo de Imagem</label>
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="all">Todos</option>
                    <option value="rgb">RGB</option>
                    <option value="nir">NIR</option>
                    <option value="multispectral">Multiespectral</option>
                    <option value="thermal">Termal</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="all">Todos</option>
                    <option value="pending">Pendente</option>
                    <option value="processing">Processando</option>
                    <option value="completed">Concluído</option>
                    <option value="failed">Erro</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Ordenar por</label>
                  <select
                    value={`${sortBy}-${sortOrder}`}
                    onChange={(e) => {
                      const [by, order] = e.target.value.split('-');
                      setSortBy(by as any);
                      setSortOrder(order as any);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="date-desc">Data (mais recente)</option>
                    <option value="date-asc">Data (mais antigo)</option>
                    <option value="name-asc">Nome (A-Z)</option>
                    <option value="name-desc">Nome (Z-A)</option>
                    <option value="type-asc">Tipo (A-Z)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Images Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredImages.map((image) => (
                <div key={image.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                  {/* Image Preview */}
                  <div className="aspect-video bg-gray-100 relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Camera className="w-12 h-12 text-gray-400" />
                    </div>
                    <div className="absolute top-3 left-3">
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getTypeColor(image.image_type)}`}>
                        {image.image_type.toUpperCase()}
                      </span>
                    </div>
                    <div className="absolute top-3 right-3">
                      {getStatusIcon(image.processing_status)}
                    </div>
                  </div>

                  {/* Image Info */}
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium text-gray-900 truncate">{image.field_name}</h3>
                      {getTypeIcon(image.image_type)}
                    </div>

                    <p className="text-sm text-gray-600 mb-3 truncate">{image.original_filename}</p>

                    <div className="space-y-2 text-xs text-gray-500">
                      {image.capture_date && (
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{new Date(image.capture_date).toLocaleString('pt-BR')}</span>
                        </div>
                      )}
                      {image.latitude && image.longitude && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-3 h-3" />
                          <span>{image.latitude.toFixed(4)}, {image.longitude.toFixed(4)}</span>
                        </div>
                      )}
                      {image.weather_conditions && (
                        <div className="flex items-center space-x-1">
                          <span>🌤️</span>
                          <span className="truncate">{image.weather_conditions}</span>
                        </div>
                      )}
                    </div>

                    {/* Analysis Results Summary */}
                    {image.analysis_results && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {image.analysis_results.ndvi_analysis && (
                            <div className="bg-green-50 rounded p-2">
                              <div className="font-medium text-green-800">NDVI</div>
                              <div className="text-green-600">{image.analysis_results.ndvi_analysis.mean_ndvi.toFixed(2)}</div>
                            </div>
                          )}
                          {image.analysis_results.pest_detection && (
                            <div className="bg-red-50 rounded p-2">
                              <div className="font-medium text-red-800">Pragas</div>
                              <div className="text-red-600">{image.analysis_results.pest_detection.total_detections} detectadas</div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="mt-4 flex items-center space-x-2">
                      <button className="flex-1 bg-emerald-600 text-white py-2 px-3 rounded-lg hover:bg-emerald-700 transition-colors text-sm flex items-center justify-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>Visualizar</span>
                      </button>

                      {image.processing_status === 'completed' && !analysisProgress[image.id] && (
                        <button
                          onClick={() => handleAnalyzeImage(image.id)}
                          className="bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center space-x-1"
                        >
                          <BarChart3 className="w-4 h-4" />
                          <span>Analisar</span>
                        </button>
                      )}

                      {analysisProgress[image.id] && (
                        <div className="bg-yellow-100 text-yellow-800 py-2 px-3 rounded-lg text-sm flex items-center space-x-1">
                          <Clock className="w-4 h-4 animate-spin" />
                          <span>Analisando...</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredImages.length === 0 && (
              <div className="text-center py-12">
                <Camera className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhuma imagem encontrada</h3>
                <p className="text-gray-600">
                  {images.length === 0
                    ? 'Faça upload de imagens de drone para começar a análise'
                    : 'Tente ajustar os filtros para encontrar as imagens desejadas'
                  }
                </p>
              </div>
            )}
          </>
        )}

        {!selectedFarm && (
          <div className="text-center py-12">
            <Target className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Selecione uma fazenda</h3>
            <p className="text-gray-600">
              Escolha uma fazenda para fazer upload e analisar imagens de drone
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DroneImageAnalysis;
