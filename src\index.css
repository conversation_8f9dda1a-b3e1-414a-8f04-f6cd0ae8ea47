@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animações para marcadores de localização de precisão */
@keyframes pulse-location {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
    box-shadow: 0 3px 8px rgba(0,0,0,0.4), 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
    box-shadow: 0 3px 8px rgba(0,0,0,0.4), 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Estilos para marcadores customizados */
.precision-marker {
  background: transparent !important;
  border: none !important;
}

.user-location-marker {
  background: transparent !important;
  border: none !important;
}

.drone-marker {
  background: transparent !important;
  border: none !important;
}

.custom-div-icon {
  background: transparent !important;
  border: none !important;
}

/* Estilos para popups do Leaflet */
.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-width: 400px !important;
  max-height: 500px !important;
}

.leaflet-popup-content {
  margin: 0 !important;
  max-width: 380px !important;
  max-height: 480px !important;
  overflow-y: auto;
}

.leaflet-popup-tip {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Popup específico para drones */
.drone-popup .leaflet-popup-content-wrapper {
  max-width: 420px !important;
  max-height: 600px !important;
}

.drone-popup .leaflet-popup-content {
  max-width: 400px !important;
  max-height: 580px !important;
}

/* Scrollbar customizada para popups */
.leaflet-popup-content::-webkit-scrollbar {
  width: 6px;
}

.leaflet-popup-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.leaflet-popup-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.leaflet-popup-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
